# Requirements Document

## Introduction

The dashboard overview API currently has broken date filtering functionality. When users request data for specific date ranges like "last_year", the API returns data that doesn't match the requested time period. This feature will fix and enhance the date filtering system to ensure all date range filters work accurately and robustly across the dashboard analytics.

## Requirements

### Requirement 1

**User Story:** As a dashboard user, I want to filter analytics data by "last_year" and receive only data from the previous 12 months, so that I can analyze year-over-year performance accurately.

#### Acceptance Criteria

1. WHEN a user requests dashboard data with date_range=last_year THEN the system SHALL return only data from exactly 365 days ago to today
2. WHEN the last_year filter is applied THEN recipe views, impressions, bookmarks, and contact submissions SHALL only include records within the last 365 days
3. WHEN no data exists for the last year period THEN the system SHALL return zero counts and empty arrays rather than unfiltered data

### Requirement 2

**User Story:** As a dashboard user, I want to use various date range filters (last_month, last_week, last_30_days, etc.) and get consistent, accurate results, so that I can analyze trends across different time periods.

#### Acceptance Criteria

1. WHEN a user specifies date_range=last_month THEN the system SHALL return data from exactly 30 days ago to today
2. WHEN a user specifies date_range=last_week THEN the system SHALL return data from exactly 7 days ago to today
3. WHEN a user specifies date_range=last_30_days THEN the system SHALL return data from exactly 30 days ago to today
4. WHEN a user specifies date_range=today THEN the system SHALL return data from the current day only
5. WHEN an invalid date range is provided THEN the system SHALL return an error with clear messaging

### Requirement 3

**User Story:** As a dashboard user, I want all analytics components (stats, charts, recent activity) to respect the same date filter, so that I get a consistent view of data across all dashboard sections.

#### Acceptance Criteria

1. WHEN a date range filter is applied THEN totalRecipes count SHALL only include recipes created within the specified period
2. WHEN a date range filter is applied THEN topCategory SHALL be calculated only from recipes within the specified period
3. WHEN a date range filter is applied THEN highestImpressionRecipe SHALL consider only impressions within the specified period
4. WHEN a date range filter is applied THEN recipeViewsTrend SHALL include only views within the specified period
5. WHEN a date range filter is applied THEN categoryPerformance SHALL calculate metrics only from the specified period
6. WHEN a date range filter is applied THEN userEngagementHeatmap SHALL show only engagement within the specified period
7. WHEN a date range filter is applied THEN conversionFunnel SHALL calculate conversions only within the specified period
8. WHEN a date range filter is applied THEN recentActivity SHALL show only activities within the specified period

### Requirement 4

**User Story:** As a developer, I want robust date handling that accounts for timezone differences and edge cases, so that the filtering works consistently regardless of user location or system configuration.

#### Acceptance Criteria

1. WHEN calculating date ranges THEN the system SHALL use UTC timestamps for consistency
2. WHEN handling date boundaries THEN the system SHALL include the full start and end days (00:00:00 to 23:59:59)
3. WHEN processing date filters THEN the system SHALL handle leap years correctly
4. WHEN no date_range parameter is provided THEN the system SHALL default to a reasonable period (e.g., last_30_days)
5. IF database queries fail due to date formatting THEN the system SHALL log the error and return appropriate error responses

### Requirement 5

**User Story:** As a system administrator, I want comprehensive logging and error handling for date filtering operations, so that I can troubleshoot issues and monitor system performance.

#### Acceptance Criteria

1. WHEN date range calculations are performed THEN the system SHALL log the calculated start and end dates
2. WHEN invalid date parameters are received THEN the system SHALL log the invalid input and return descriptive error messages
3. WHEN database queries are executed with date filters THEN the system SHALL log query performance metrics
4. IF date filtering operations fail THEN the system SHALL return structured error responses with appropriate HTTP status codes