import * as cron from 'node-cron';
import { recipeCostFreshnessCronJob } from './recipeCostFreshnessCron';

/**
 * Initialize and start all cron jobs
 */
export const initializeCronJobs = (): void => {
  console.log('Initializing cron jobs...');

  // Recipe Cost Freshness Cron Job
  // Runs based on COST_RECALCULATION_CRON configuration
  const costRecalculationSchedule = global.config.COST_RECALCULATION_CRON || '0 * * * *'; // Default: every hour

  cron.schedule(costRecalculationSchedule, async () => {
    try {
      console.log(`Starting scheduled recipe cost freshness update at ${new Date().toISOString()}`);
      await recipeCostFreshnessCronJob();
    } catch (error) {
      console.error('Recipe cost freshness cron job failed:', error);
    }
  }, {
    scheduled: true,
    timezone: global.config.TIMEZONE || 'UTC'
  });

  console.log(`Recipe cost freshness cron job scheduled: ${costRecalculationSchedule} (${global.config.TIMEZONE || 'UTC'})`);
};

/**
 * Stop all cron jobs (useful for graceful shutdown)
 */
export const stopAllCronJobs = (): void => {
  console.log('Stopping all cron jobs...');
  cron.getTasks().forEach((task: any) => {
    task.stop();
  });
  console.log('All cron jobs stopped');
};

export default {
  initializeCronJobs,
  stopAllCronJobs
};
