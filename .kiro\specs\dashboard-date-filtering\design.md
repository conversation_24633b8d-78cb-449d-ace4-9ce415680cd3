# Design Document

## Overview

The dashboard date filtering system currently has inconsistencies where the `getDateRange` method correctly calculates date ranges, but the database queries don't properly apply these filters across all dashboard components. This design addresses the systematic fixing of date filtering to ensure all analytics data respects the requested date ranges.

## Architecture

### Current Architecture Issues
1. **Inconsistent Date Application**: While `getDateRange` calculates correct dates, some queries ignore the date filters
2. **Missing Date Filters**: Several database queries don't include date filtering clauses
3. **Timezone Inconsistencies**: Mix of local time and UTC handling across different queries
4. **Incomplete Filter Coverage**: Not all dashboard components apply date filters to their underlying data

### Proposed Architecture
```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Controller    │───▶│  Date Validator  │───▶│  Date Calculator│
│                 │    │                  │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                                               │
         ▼                                               ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│ Analytics       │───▶│  Query Builder   │───▶│  Database       │
│ Service         │    │  with Date       │    │  Queries        │
│                 │    │  Filters         │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## Components and Interfaces

### 1. Date Range Validator
**Purpose**: Validate and normalize date range parameters
**Location**: `src/helper/date.helper.ts`

```typescript
interface DateRangeValidator {
  validateDateRange(dateRange: string): boolean;
  getValidDateRanges(): string[];
  sanitizeDateRange(dateRange: string): string;
}
```

### 2. Enhanced Date Calculator
**Purpose**: Centralized date range calculations with UTC consistency
**Location**: Enhanced `getDateRange` method in `analytics.service.ts`

```typescript
interface DateRangeResult {
  startDate: Date;
  endDate: Date;
  dateRange: string;
  isUTC: boolean;
}

interface DateCalculator {
  calculateDateRange(dateRange: string, organizationId?: string): Promise<DateRangeResult>;
  convertToUTC(date: Date): Date;
  formatDateForQuery(date: Date): string;
}
```

### 3. Query Filter Builder
**Purpose**: Standardized date filter application across all queries
**Location**: `src/helper/query.helper.ts`

```typescript
interface QueryFilter {
  dateColumn: string;
  startDate: Date;
  endDate: Date;
  includeTime: boolean;
}

interface QueryFilterBuilder {
  buildDateFilter(filter: QueryFilter): string;
  buildReplacementParams(filter: QueryFilter): object;
  combineFilters(filters: QueryFilter[]): string;
}
```

### 4. Analytics Query Standardizer
**Purpose**: Ensure all analytics queries use consistent date filtering
**Location**: Enhanced methods in `analytics.service.ts`

## Data Models

### Date Range Configuration
```typescript
interface DateRangeConfig {
  name: string;
  days?: number;
  isFinancialYear?: boolean;
  isCalendarYear?: boolean;
  customLogic?: (organizationId?: string) => Promise<{startDate: Date, endDate: Date}>;
}
```

### Query Date Filter
```typescript
interface QueryDateFilter {
  tableName: string;
  dateColumn: string;
  startDate: Date;
  endDate: Date;
  timezone: 'UTC' | 'local';
}
```

## Error Handling

### Date Validation Errors
- Invalid date range parameters return structured error responses
- Malformed custom date inputs are sanitized or rejected
- Database date query failures are logged with context

### Fallback Strategies
- Invalid date ranges default to `last_30_days`
- Failed date calculations return safe default ranges
- Database query failures return empty results rather than unfiltered data

### Error Response Format
```typescript
interface DateFilterError {
  status: false;
  message: string;
  errorType: 'INVALID_DATE_RANGE' | 'DATE_CALCULATION_ERROR' | 'QUERY_ERROR';
  validOptions?: string[];
  timestamp: string;
}
```

## Testing Strategy

### Unit Tests
- Date range calculation accuracy for all supported ranges
- UTC conversion consistency
- Edge case handling (leap years, month boundaries)
- Financial year calculations with different start months

### Integration Tests
- End-to-end dashboard API calls with various date ranges
- Database query result verification against expected date filters
- Organization-specific financial year handling

### Performance Tests
- Query performance with date filters applied
- Large dataset filtering efficiency
- Concurrent request handling with different date ranges

## Implementation Details

### Phase 1: Core Date Infrastructure
1. Create centralized date validation helper
2. Enhance existing `getDateRange` method with UTC consistency
3. Create query filter builder utility
4. Add comprehensive error handling

### Phase 2: Query Standardization
1. Update all analytics queries to use standardized date filters
2. Ensure consistent date column usage across queries
3. Add missing date filters to queries that currently ignore them
4. Implement proper timezone handling

### Phase 3: Validation and Testing
1. Add input validation for all date range parameters
2. Implement comprehensive error responses
3. Add logging for date filter operations
4. Create test coverage for all date scenarios

### Database Query Pattern
All analytics queries will follow this standardized pattern:
```sql
SELECT ...
FROM table t
WHERE t.date_column BETWEEN :startDate AND :endDate
  AND [other conditions]
  AND [organization filter]
ORDER BY t.date_column DESC
```

### UTC Consistency Rules
1. All date calculations performed in UTC
2. Database queries use UTC timestamps
3. Response timestamps include timezone information
4. Client-side date display handles timezone conversion

## Security Considerations

### Input Sanitization
- All date range parameters validated against whitelist
- Custom date inputs sanitized to prevent injection
- Organization ID validation for financial year settings

### Data Access Control
- Date filters combined with organization filters
- No data leakage across organization boundaries
- Audit logging for date filter operations

## Performance Optimizations

### Database Indexing
- Ensure proper indexes on date columns used in filters
- Composite indexes for date + organization_id combinations
- Query execution plan analysis for date-filtered queries

### Caching Strategy
- Cache date range calculations for common ranges
- Organization-specific financial year settings caching
- Query result caching with date-aware cache keys

### Query Optimization
- Use BETWEEN clauses for date ranges instead of >= AND <=
- Minimize date function calls in WHERE clauses
- Optimize JOIN operations with date filters