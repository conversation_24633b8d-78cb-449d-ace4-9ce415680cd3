import { sequelize } from "../models";
import { Recipe, RecipeStatus } from "../models/Recipe";
import { Ingredient, IngredientStatus } from "../models/Ingreditant";
import { RecipeIngredients, RecipeIngredientsStatus } from "../models/RecipeIngredients";
import { RecipeMeasure } from "../models/RecipeMeasure";
import { updateRecipeCostTimestamp } from "../helper/timestamp.helper";

/**
 * Unit conversion factors for cost calculations
 * Based on your frontend conversion logic
 */
const conversions = {
  // Weight (base: g)
  g: { type: 'weight', factor: 1 },
  kg: { type: 'weight', factor: 1000 },
  oz: { type: 'weight', factor: 28.3495 },
  lb: { type: 'weight', factor: 453.592 },

  // Volume (base: ml)
  ml: { type: 'volume', factor: 1 },
  l: { type: 'volume', factor: 1000 },
  liter: { type: 'volume', factor: 1000 },
  tsp: { type: 'volume', factor: 4.92892 },
  tbsp: { type: 'volume', factor: 14.7868 },
  cup: { type: 'volume', factor: 236.588 },
  pint: { type: 'volume', factor: 473.176 },
  quart: { type: 'volume', factor: 946.353 },
  gallon: { type: 'volume', factor: 3785.41 },
  pinch: { type: 'volume', factor: 0.3 },

  // Piece (base: unit)
  unit: { type: 'piece', factor: 1 },
  each: { type: 'piece', factor: 1 },
  clove: { type: 'piece', factor: 1 },
  ball: { type: 'piece', factor: 1 },
  slice: { type: 'piece', factor: 1 },
  serving: { type: 'piece', factor: 1 },
  leaf: { type: 'piece', factor: 1 },
  wedge: { type: 'piece', factor: 1 },
  sprig: { type: 'piece', factor: 1 },
  pack: { type: 'piece', factor: 1 },
  case: { type: 'piece', factor: 1 },
  dozen: { type: 'piece', factor: 12 },
};

/**
 * Convert cost between different units
 */
export const convertCost = (
  cost: number,
  fromUnitSlug: string,
  toUnitSlug: string,
  density: number = 1
): number => {
  const fromUnit = conversions[fromUnitSlug?.toLowerCase() as keyof typeof conversions];
  const toUnit = conversions[toUnitSlug?.toLowerCase() as keyof typeof conversions];

  if (!fromUnit || !toUnit) return cost;

  const sameType = fromUnit.type === toUnit.type;

  if (sameType) {
    // Convert within the same type (e.g., kg -> g or l -> ml)
    const costPerBase = cost / fromUnit.factor;
    return costPerBase * toUnit.factor;
  }

  // Cross conversion between weight <-> volume using density
  const isWeightToVolume = fromUnit.type === 'weight' && toUnit.type === 'volume';
  const isVolumeToWeight = fromUnit.type === 'volume' && toUnit.type === 'weight';

  if (isWeightToVolume || isVolumeToWeight) {
    const weightInG = isWeightToVolume ? 1 : density;
    const volumeInMl = isWeightToVolume ? density : 1;

    const crossFactor = (toUnit.factor / fromUnit.factor) * (volumeInMl / weightInG);
    return cost * crossFactor;
  }

  // Incompatible types (e.g., weight -> piece)
  return cost;
};

/**
 * Calculate ingredient cost for a recipe ingredient
 */
export const calculateIngredientCost = async (
  ingredientId: number,
  recipeQuantity: number,
  recipeMeasureId: number,
  organizationId: string,
  transaction?: any
): Promise<number> => {
  try {
    // Get ingredient details with its base unit
    const ingredient = await Ingredient.findOne({
      where: {
        id: ingredientId,
        organization_id: organizationId,
        ingredient_status: IngredientStatus.active
      },
      include: [{
        model: RecipeMeasure,
        as: 'baseUnit',
        where: { id: sequelize.col('Ingredient.unit_of_measure') },
        required: false
      }],
      transaction
    });

    if (!ingredient) {
      throw new Error(`Ingredient with ID ${ingredientId} not found`);
    }

    // Get recipe measure unit
    const recipeMeasure = await RecipeMeasure.findOne({
      where: { id: recipeMeasureId },
      transaction
    });

    if (!recipeMeasure) {
      throw new Error(`Recipe measure with ID ${recipeMeasureId} not found`);
    }

    const baseUnit = ingredient.baseUnit;
    if (!baseUnit) {
      throw new Error(`Base unit not found for ingredient ${ingredientId}`);
    }

    // Convert cost from ingredient's base unit to recipe unit
    const convertedCost = convertCost(
      ingredient.cost_per_unit,
      baseUnit.measure_slug,
      recipeMeasure.measure_slug,
      1 // Default density, could be enhanced to get from ingredient properties
    );

    // Calculate total cost for the quantity used in recipe
    return convertedCost * recipeQuantity;

  } catch (error) {
    console.error(`Error calculating ingredient cost:`, error);
    throw error;
  }
};

/**
 * Recalculate all costs for a recipe when freshness is outdated
 */
export const recalculateRecipeCosts = async (
  recipeId: number,
  organizationId: string,
  transaction?: any
): Promise<{
  success: boolean;
  totalCost: number;
  updatedIngredients: number;
  errors: string[];
}> => {
  const errors: string[] = [];
  let updatedIngredients = 0;
  let totalCost = 0;

  try {
    // Get all active recipe ingredients
    const recipeIngredients = await RecipeIngredients.findAll({
      where: {
        recipe_id: recipeId,
        recipe_ingredient_status: RecipeIngredientsStatus.active,
        organization_id: organizationId
      },
      include: [{
        model: Ingredient,
        as: 'ingredient',
        where: { ingredient_status: IngredientStatus.active }
      }],
      transaction
    });

    // Calculate cost for each ingredient
    for (const recipeIngredient of recipeIngredients) {
      try {
        const newCost = await calculateIngredientCost(
          recipeIngredient.ingredient_id,
          recipeIngredient.ingredient_quantity || 0,
          recipeIngredient.ingredient_measure || 0,
          organizationId,
          transaction
        );

        // Update the ingredient cost in recipe_ingredients table
        await recipeIngredient.update({
          ingredient_cost: newCost,
          updated_by: 1 // System update
        }, { transaction });

        totalCost += newCost;
        updatedIngredients++;

      } catch (error: any) {
        const errorMsg = `Failed to calculate cost for ingredient ${recipeIngredient.ingredient_id}: ${error.message}`;
        errors.push(errorMsg);
        console.error(errorMsg);
      }
    }

    // Update recipe cost timestamp
    await updateRecipeCostTimestamp(recipeId, transaction);

    return {
      success: errors.length === 0,
      totalCost,
      updatedIngredients,
      errors
    };

  } catch (error) {
    console.error(`Error recalculating recipe costs for recipe ${recipeId}:`, error);
    throw error;
  }
};

/**
 * Auto-update recipe costs when ingredient costs change
 */
export const autoUpdateRecipeCosts = async (
  ingredientId: number,
  organizationId: string,
  transaction?: any
): Promise<{
  success: boolean;
  updatedRecipes: number[];
  errors: string[];
}> => {
  const errors: string[] = [];
  const updatedRecipes: number[] = [];

  try {
    // Find all recipes that use this ingredient
    const affectedRecipes = await sequelize.query(`
      SELECT DISTINCT r.id, r.recipe_title
      FROM mo_recipe r
      JOIN mo_recipe_ingredients ri ON r.id = ri.recipe_id
      WHERE ri.ingredient_id = :ingredientId
        AND r.organization_id = :organizationId
        AND ri.recipe_ingredient_status = '${RecipeIngredientsStatus.active}'
        AND r.recipe_status = '${RecipeStatus.publish}'
    `, {
      replacements: { ingredientId, organizationId },
      type: sequelize.QueryTypes.SELECT,
      transaction
    });

    // Update costs for each affected recipe
    for (const recipe of affectedRecipes as any[]) {
      try {
        const result = await recalculateRecipeCosts(
          recipe.id,
          organizationId,
          transaction
        );

        if (result.success) {
          updatedRecipes.push(recipe.id);
        } else {
          errors.push(`Recipe ${recipe.recipe_title}: ${result.errors.join(', ')}`);
        }

      } catch (error: any) {
        const errorMsg = `Failed to update recipe ${recipe.recipe_title}: ${error.message}`;
        errors.push(errorMsg);
        console.error(errorMsg);
      }
    }

    return {
      success: errors.length === 0,
      updatedRecipes,
      errors
    };

  } catch (error) {
    console.error(`Error auto-updating recipe costs for ingredient ${ingredientId}:`, error);
    throw error;
  }
};

export default {
  convertCost,
  calculateIngredientCost,
  recalculateRecipeCosts,
  autoUpdateRecipeCosts
};
