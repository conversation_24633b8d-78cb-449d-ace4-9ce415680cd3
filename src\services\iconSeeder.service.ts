import fs from "fs";
import path from "path";
import {
  getHash,
  RECIPE_FILE_UPLOAD_CONSTANT,
  getMimeTypeFromExtension,
} from "../helper/common";
// NEW BOOTSTRAP: ensure global config/db are set BEFORE we load models --------------------------------------
import configJson from "../../shared/config/config.json";
import dbConfigJson from "../../shared/config/db.json";
import dotenv from "dotenv";

dotenv.config();

const ENV = process.env.NODE_ENV || "staging";

// Only set when they have not been initialised by the main server process
if (!(global as any).config) {
  // Deep-clone to avoid accidental mutation of the shared object elsewhere
  (global as any).config = JSON.parse(JSON.stringify(configJson))[ENV];
}
if (!(global as any).db) {
  (global as any).db = JSON.parse(JSON.stringify(dbConfigJson))[ENV];
}

// --------------------------------------------------------------------------------------
// NOTE: We import (require) the Sequelize models *after* config/db are guaranteed to exist
// This allows this file to run standalone via ts-node while still working when imported
// by the main Express application (where global config/db are pre-initialised)
// --------------------------------------------------------------------------------------
// eslint-disable-next-line @typescript-eslint/no-var-requires, @typescript-eslint/no-require-imports
const { db } = require("../models/index");
// --------------------------------------------------------------------------------------
// (Everything below this comment is unchanged service implementation)
import {
  item_type,
  item_status,
  item_IEC,
  item_external_location,
  item_category,
} from "../models/Item";
import uploadService from "../helper/upload.service";

interface IconUploadResult {
  success: boolean;
  iconId?: number;
  error?: string;
  status: "uploaded" | "exists" | "skipped" | "failed";
  details?: string;
}

interface BatchUploadResult {
  category: string;
  total: number;
  success: number;
  skipped: number;
  failed: number;
  results: Array<{
    name: string;
    result: IconUploadResult;
  }>;
}

/**
 * Icon Seeder Service
 * Uses existing upload infrastructure and follows project standards
 */
class IconSeederService {
  private readonly bucketName: string;

  constructor() {
    this.bucketName = process.env.NODE_ENV || "development";
    console.log(
      ` Using bucket: ${this.bucketName} (NODE_ENV: ${process.env.NODE_ENV})`
    );
  }

  /**
   * Upload icon for any entity using existing upload service
   */
  private async uploadIcon(
    entityName: string,
    iconFileName: string,
    iconsBasePath: string,
    destinationPath: string,
    itemCategory: item_category
  ): Promise<IconUploadResult> {
    try {
      const iconPath = path.join(iconsBasePath, iconFileName);

      // Validate file exists
      if (!fs.existsSync(iconPath)) {
        return {
          success: false,
          status: "failed",
          error: `Icon file not found: ${iconPath}`,
        };
      }

      // Read file
      const fileBuffer = fs.readFileSync(iconPath);
      const fileStats = fs.statSync(iconPath);
      const fileExtension = path.extname(iconFileName);

      // Create file object for hash generation
      const fileObject = {
        path: iconPath,
        size: fileStats.size,
        originalname: path.basename(iconPath),
      };

      // Generate hash using existing helper
      const fileHashResult = await getHash(fileObject);
      if (!fileHashResult.status) {
        return {
          success: false,
          status: "failed",
          error: `Failed to generate hash: ${fileHashResult.message}`,
        };
      }

      // Check for existing item with same hash (avoid duplicates)
      const existingItem = await db.Item.findOne({
        where: {
          item_hash: fileHashResult.hash,
          item_organization_id: null,
        },
      });

      if (existingItem) {
        // If existing item path already safe (no ampersand), reuse it. Otherwise re-upload with new safe path
        if (!existingItem.item_location.includes("&")) {
          console.log(
            `✅ ${entityName}: Icon already exists, reusing item ID: ${existingItem.id}`
          );
          return {
            success: true,
            iconId: existingItem.id,
            status: "exists",
            details: "Reused existing icon",
          };
        }
        // Else continue to upload a new item with sanitized filename
        console.log(
          `ℹ️  ${entityName}: Existing icon contains unsafe characters – uploading sanitized version.`
        );
      }

      // Use existing upload service to upload file
      const uploadResult = await uploadService.uploadFileToBucket(
        this.bucketName,
        destinationPath,
        fileBuffer,
        fileHashResult.actualMimeType || getMimeTypeFromExtension(fileExtension)
      );

      if (!uploadResult.success) {
        return {
          success: false,
          status: "failed",
          error: `Upload failed: ${uploadResult.error}`,
        };
      }

      console.log(
        `📤 ${entityName}: Uploaded to S3: ${destinationPath} (bucket: ${this.bucketName})`
      );

      // Create Item record following existing patterns
      const storageFileName = `${slugifyForFileName(entityName)}-icon.png`;
      const itemData = {
        item_type: item_type.IMAGE,
        item_name: storageFileName,
        item_hash: fileHashResult.hash,
        item_mime_type:
          fileHashResult.actualMimeType ||
          getMimeTypeFromExtension(fileExtension),
        item_extension: fileExtension,
        item_size: fileBuffer.length,
        item_IEC: item_IEC.B,
        item_status: item_status.ACTIVE,
        item_external_location: item_external_location.NO,
        item_location: destinationPath,
        item_organization_id: null,
        item_category: itemCategory,
        created_by: 1,
        updated_by: 1,
      };

      const item = await db.Item.create(itemData);
      console.log(`✅ ${entityName}: Created item record with ID: ${item.id}`);

      return {
        success: true,
        iconId: item.id,
        status: "uploaded",
        details: `Successfully uploaded and created item ${item.id}`,
      };
    } catch (error: any) {
      console.error(`❌ ${entityName}: Upload error: ${error.message}`);
      return {
        success: false,
        status: "failed",
        error: error.message,
      };
    }
  }

  /**
   * Upload recipe category icon using standard upload constants
   */
  async uploadRecipeCategoryIcon(
    categoryName: string,
    iconFileName: string
  ): Promise<number | null> {
    const storageFileName = `${slugifyForFileName(categoryName)}-icon.png`;
    const destinationPath =
      RECIPE_FILE_UPLOAD_CONSTANT.CATEGORY_ICON.destinationPath(
        null, // organization name (null for system defaults)
        null, // category ID (not needed for file path)
        storageFileName
      );

    const result = await this.uploadIcon(
      categoryName,
      iconFileName,
      "src/icons/recipe_category",
      destinationPath,
      item_category.CATEGORY_ICON
    );

    return result.success ? result.iconId! : null;
  }

  /**
   * Upload ingredient category icon using standard upload constants
   */
  async uploadIngredientCategoryIcon(
    categoryName: string,
    iconFileName: string
  ): Promise<number | null> {
    const storageFileName = `${slugifyForFileName(categoryName)}-icon.png`;
    const destinationPath =
      RECIPE_FILE_UPLOAD_CONSTANT.INGREDIENT_CATEGORY_ICON.destinationPath(
        null, // organization name (null for system defaults)
        null, // category ID (not needed for file path)
        storageFileName
      );

    const result = await this.uploadIcon(
      categoryName,
      iconFileName,
      "src/icons/ingredient_category",
      destinationPath,
      item_category.CATEGORY_ICON
    );

    return result.success ? result.iconId! : null;
  }

  /**
   * Upload attribute icon using standard upload constants
   */
  async uploadAttributeIcon(
    attributeName: string,
    iconFileName: string
  ): Promise<number | null> {
    const storageFileName = `${slugifyForFileName(attributeName)}-icon.png`;
    const destinationPath =
      RECIPE_FILE_UPLOAD_CONSTANT.ATTRIBUTE_ICON.destinationPath(
        null, // organization name (null for system defaults)
        null, // attribute ID (not needed for file path)
        storageFileName
      );

    const result = await this.uploadIcon(
      attributeName,
      iconFileName,
      "src/icons/Allergens",
      destinationPath,
      item_category.ATTRIBUTE_ICON
    );

    return result.success ? result.iconId! : null;
  }

  /**
   * Batch upload all recipe category icons
   */
  async batchUploadRecipeCategoryIcons(
    force = false
  ): Promise<BatchUploadResult> {
    console.log("📤 Batch uploading recipe category icons...");

    try {
      const categories = await db.sequelize.query(
        `SELECT category_name, category_icon FROM mo_category WHERE category_type = 'recipe' AND organization_id IS NULL AND is_system_category = true`,
        { type: db.Sequelize.QueryTypes.SELECT }
      );

      const iconMapping = this.getRecipeCategoryIconMapping();
      const results: BatchUploadResult = {
        category: "Recipe Categories",
        total: (categories as any[]).length,
        success: 0,
        skipped: 0,
        failed: 0,
        results: [],
      };

      for (const category of categories as any[]) {
        const iconFileName = iconMapping[category.category_name];

        if (!iconFileName) {
          results.results.push({
            name: category.category_name,
            result: {
              success: false,
              status: "skipped",
              error: "No icon mapping found",
            },
          });
          results.skipped++;
          continue;
        }

        if (category.category_icon && !force) {
          results.results.push({
            name: category.category_name,
            result: {
              success: true,
              status: "exists",
              details: "Already has icon",
            },
          });
          results.skipped++;
          continue;
        }

        try {
          const iconId = await this.uploadRecipeCategoryIcon(
            category.category_name,
            iconFileName
          );

          if (iconId) {
            await db.sequelize.query(
              `UPDATE mo_category SET category_icon = :iconId WHERE category_name = :categoryName AND category_type = 'recipe' AND organization_id IS NULL`,
              {
                replacements: {
                  iconId,
                  categoryName: category.category_name,
                },
                type: db.Sequelize.QueryTypes.UPDATE,
              }
            );

            results.results.push({
              name: category.category_name,
              result: {
                success: true,
                iconId,
                status: "uploaded",
                details: `Uploaded as ${iconFileName}`,
              },
            });
            results.success++;
          } else {
            results.results.push({
              name: category.category_name,
              result: {
                success: false,
                status: "failed",
                error: "Upload failed",
              },
            });
            results.failed++;
          }
        } catch (error: any) {
          results.results.push({
            name: category.category_name,
            result: {
              success: false,
              status: "failed",
              error: error.message,
            },
          });
          results.failed++;
        }
      }

      return results;
    } catch (error: any) {
      console.error(
        "❌ Error in batchUploadRecipeCategoryIcons:",
        error.message
      );
      return {
        category: "Recipe Categories",
        total: 0,
        success: 0,
        skipped: 0,
        failed: 0,
        results: [],
      };
    }
  }

  /**
   * Batch upload all ingredient category icons
   */
  async batchUploadIngredientCategoryIcons(
    force = false
  ): Promise<BatchUploadResult> {
    console.log("📤 Batch uploading ingredient category icons...");

    try {
      const categories = await db.sequelize.query(
        `SELECT category_name, category_icon FROM mo_category WHERE category_type = 'ingredient' AND organization_id IS NULL AND is_system_category = true`,
        { type: db.Sequelize.QueryTypes.SELECT }
      );

      const iconMapping = this.getIngredientCategoryIconMapping();
      const results: BatchUploadResult = {
        category: "Ingredient Categories",
        total: (categories as any[]).length,
        success: 0,
        skipped: 0,
        failed: 0,
        results: [],
      };

      for (const category of categories as any[]) {
        const iconFileName = iconMapping[category.category_name];

        if (!iconFileName) {
          results.results.push({
            name: category.category_name,
            result: {
              success: false,
              status: "skipped",
              error: "No icon mapping found",
            },
          });
          results.skipped++;
          continue;
        }

        if (category.category_icon && !force) {
          results.results.push({
            name: category.category_name,
            result: {
              success: true,
              status: "exists",
              details: "Already has icon",
            },
          });
          results.skipped++;
          continue;
        }

        try {
          const iconId = await this.uploadIngredientCategoryIcon(
            category.category_name,
            iconFileName
          );

          if (iconId) {
            await db.sequelize.query(
              `UPDATE mo_category SET category_icon = :iconId WHERE category_name = :categoryName AND category_type = 'ingredient' AND organization_id IS NULL`,
              {
                replacements: {
                  iconId,
                  categoryName: category.category_name,
                },
                type: db.Sequelize.QueryTypes.UPDATE,
              }
            );

            results.results.push({
              name: category.category_name,
              result: {
                success: true,
                iconId,
                status: "uploaded",
                details: `Uploaded as ${iconFileName}`,
              },
            });
            results.success++;
          } else {
            results.results.push({
              name: category.category_name,
              result: {
                success: false,
                status: "failed",
                error: "Upload failed",
              },
            });
            results.failed++;
          }
        } catch (error: any) {
          results.results.push({
            name: category.category_name,
            result: {
              success: false,
              status: "failed",
              error: error.message,
            },
          });
          results.failed++;
        }
      }

      return results;
    } catch (error: any) {
      console.error(
        "❌ Error in batchUploadIngredientCategoryIcons:",
        error.message
      );
      return {
        category: "Ingredient Categories",
        total: 0,
        success: 0,
        skipped: 0,
        failed: 0,
        results: [],
      };
    }
  }

  /**
   * Batch upload all allergen icons
   */
  async batchUploadAllergenIcons(force = false): Promise<BatchUploadResult> {
    console.log("📤 Batch uploading allergen icons...");

    try {
      const allergens = await db.sequelize.query(
        `SELECT attribute_title, attribute_icon FROM mo_food_attributes WHERE attribute_type = 'allergen' AND organization_id IS NULL AND is_system_attribute = true`,
        { type: db.Sequelize.QueryTypes.SELECT }
      );

      const iconMapping = this.getAllergenIconMapping();
      const results: BatchUploadResult = {
        category: "Allergens",
        total: (allergens as any[]).length,
        success: 0,
        skipped: 0,
        failed: 0,
        results: [],
      };

      for (const allergen of allergens as any[]) {
        const iconFileName = iconMapping[allergen.attribute_title];

        if (!iconFileName) {
          results.results.push({
            name: allergen.attribute_title,
            result: {
              success: false,
              status: "skipped",
              error: "No icon mapping found",
            },
          });
          results.skipped++;
          continue;
        }

        if (allergen.attribute_icon && !force) {
          results.results.push({
            name: allergen.attribute_title,
            result: {
              success: true,
              status: "exists",
              details: "Already has icon",
            },
          });
          results.skipped++;
          continue;
        }

        try {
          const iconId = await this.uploadAttributeIcon(
            allergen.attribute_title,
            iconFileName
          );

          if (iconId) {
            await db.sequelize.query(
              `UPDATE mo_food_attributes SET attribute_icon = :iconId WHERE attribute_title = :attributeName AND attribute_type = 'allergen' AND organization_id IS NULL`,
              {
                replacements: {
                  iconId,
                  attributeName: allergen.attribute_title,
                },
                type: db.Sequelize.QueryTypes.UPDATE,
              }
            );

            results.results.push({
              name: allergen.attribute_title,
              result: {
                success: true,
                iconId,
                status: "uploaded",
                details: `Uploaded as ${iconFileName}`,
              },
            });
            results.success++;
          } else {
            results.results.push({
              name: allergen.attribute_title,
              result: {
                success: false,
                status: "failed",
                error: "Upload failed",
              },
            });
            results.failed++;
          }
        } catch (error: any) {
          results.results.push({
            name: allergen.attribute_title,
            result: {
              success: false,
              status: "failed",
              error: error.message,
            },
          });
          results.failed++;
        }
      }

      return results;
    } catch (error: any) {
      console.error("❌ Error in batchUploadAllergenIcons:", error.message);
      return {
        category: "Allergens",
        total: 0,
        success: 0,
        skipped: 0,
        failed: 0,
        results: [],
      };
    }
  }

  /**
   * Upload all default icons (master method)
   */
  async uploadAllDefaultIcons(force = false): Promise<{
    summary: {
      totalCategories: number;
      totalSuccess: number;
      totalSkipped: number;
      totalFailed: number;
    };
    details: BatchUploadResult[];
  }> {
    console.log("🚀 Starting complete icon upload process...");

    try {
      const results: BatchUploadResult[] = [];

      // Upload all categories using existing upload service
      results.push(await this.batchUploadRecipeCategoryIcons(force));
      results.push(await this.batchUploadIngredientCategoryIcons(force));
      results.push(await this.batchUploadAllergenIcons(force));

      // Calculate summary
      const summary = {
        totalCategories: results.reduce((sum, r) => sum + r.total, 0),
        totalSuccess: results.reduce((sum, r) => sum + r.success, 0),
        totalSkipped: results.reduce((sum, r) => sum + r.skipped, 0),
        totalFailed: results.reduce((sum, r) => sum + r.failed, 0),
      };

      console.log(`\n📊 Complete Upload Summary:`);
      console.log(`   Total: ${summary.totalCategories}`);
      console.log(`   ✅ Success: ${summary.totalSuccess}`);
      console.log(`   ⏭️ Skipped: ${summary.totalSkipped}`);
      console.log(`   ❌ Failed: ${summary.totalFailed}`);

      return { summary, details: results };
    } catch (error: any) {
      console.error("❌ Error in uploadAllDefaultIcons:", error.message);
      throw error;
    }
  }

  /**
   * Get default icon mapping for recipe categories
   */
  getRecipeCategoryIconMapping(): Record<string, string> {
    return {
      "Main Course": "Main Courses Stroke Black.png",
      Appetizer: "Appetizers & Starters Stroke Black.png",
      Dessert: "Dessert cake Stroke Black.png",
      Beverage: "Beverages & Drinks Stroke Black.png",
      Soup: "Soups Stroke Black.png",
      Salad: "salad Stroke Black.png",
      Snack: "snacks Stroke Black.png",
      Breakfast: "breakfast Stroke Black.png",
      "Baked Goods": "Baked Goods Stroke Black.png",
      "Sauces & Dressings": "sauce& Dressings Stroke Black.png",
      "Side Dishes": "Side Dishes Stroke Black.png",
    };
  }

  /**
   * Get default icon mapping for ingredient categories
   */
  getIngredientCategoryIconMapping(): Record<string, string> {
    return {
      Dairy: "milk Stroke Black.png",
      Meat: "meat Stroke Black.png",
      Poultry: "Poultry Stroke Black.png",
      Seafood: "Sea Food Stroke Black.png",
      Vegetables: "vegetables Stroke Black.png",
      Fruits: "fruits Stroke Black.png",
      Grains: "Grains Stroke Black.png",
      Nuts: "Nuts & Seeds Stroke Black.png",
      "Herbs & Spices": "Herbs & Spices Stroke Black.png",
      Oils: "oil Stroke Black.png",
      Condiments: "sauce Stroke Black.png",
      Baking: "baking & sweeteners\u00A0 Stroke Black.png",
      "Dry Goods": "Dry Goods & Pulses\u00A0Stroke Black.png",
      Beverages: "Beverages & Drinks Stroke Black.png",
    };
  }

  /**
   * Get default icon mapping for allergens
   */
  getAllergenIconMapping(): Record<string, string> {
    return {
      Gluten: "gluten Stroke.png",
      Crustaceans: "Crustaceans Stroke.png",
      Eggs: "Egg Stroke.png",
      Fish: "Fish Stroke.png",
      Milk: "Milk Stroke Blue.png",
      Molluscs: "Molluscs Stroke.png",
      Peanuts: "Peanuts Stroke.png",
      "Tree Nuts": "Tree Nut Stroke.png",
      Soy: "soybean Stroke.png",
      Sesame: "Sesame Stroke.png",
      Celery: "celery Stroke.png",
      Mustard: "Mustard Stroke.png",
      Sulphites: "Sulphite Stroke.png",
      Lupin: "lupin Stroke.png",
    };
  }
}

// Utility: safer slug generator for filenames (replaces spaces, ampersands and any non-alphanumeric char with '-')
const slugifyForFileName = (str: string): string => {
  return (
    str
      .toLowerCase()
      // replace ampersand with "and" first so it's not lost
      .replace(/&/g, "-")
      // replace whitespace and other non-alphanumerics with dash
      .replace(/[^a-z0-9]+/g, "-")
      // collapse multiple dashes
      .replace(/-{2,}/g, "-")
      // trim starting/ending dashes
      .replace(/^-+|-+$/g, "")
  );
};

// Export as singleton instance following project standards
export default new IconSeederService();
