import { StatusCodes } from "http-status-codes";
import _ from "lodash";
import Jwt from "jsonwebtoken";
import { getPlatformFromRequest, getRolesMo, getUser, getUserAllRoles, getUserSession } from "../helper/common";
import { NORMAL_USER, ADMIN_SIDE_USER, ROLE_CONSTANT } from "../helper/constant";
import { QueryTypes, sequelize } from "../models";

/**
 * Check if user has platform access based on MORole permissions
 * @param user - User object
 * @param organizationId - Organization ID
 * @param platformType - Platform type (web, ios, android)
 * @returns boolean - true if user has access to the platform
 */
const checkPlatformAccess = async (user: any, organizationId: string, platformType: string): Promise<boolean> => {
  try {
    if (!user.user_role_id || !organizationId) {
      return false;
    }

    // Check if user has any permissions for the requested platform
    const platformValue = platformType === 'web' ? 1 : (platformType === 'ios' || platformType === 'android') ? 2 : 0;

    // Query permissions for the user's role and platform using raw SQL
    const permissionQuery = `
      SELECT id
      FROM mo_roles
      WHERE id = :role_id
        AND organization_id = :organization_id
        AND (platform = :platform OR platform = 3)
        AND role_status = 'active'
      LIMIT 1
    `;

    const hasPermission = await sequelize.query(permissionQuery, {
      replacements: {
        role_id: user.user_role_id,
        organization_id: organizationId,
        platform: platformValue
      },
      type: QueryTypes.SELECT,
    });

    return hasPermission && hasPermission.length > 0;
  } catch (error) {
    console.log('Error checking platform access:', error);
    return false;
  }
};

const userAuth = async (req: any, res: any, next: any) => {
  try {
    /** check Token is present  */
    if (!req.headers.authorization) {
      return res
        .status(400)
        .send({ status: false, message: res.__("ERROR_TOKEN_REQUIRED") });
    }
    const token: any = req.headers?.authorization?.split(" ")[1];

    if (req.headers.authorization) {
      const decoded: any = Jwt.decode(token);

      req.user = await getUser(decoded.user_id, true);

      if (!req?.user?.id) {
        return res
          .status(StatusCodes.UNAUTHORIZED)
          .json({ status: false, message: res.__("ERROR_INVALID_TOKEN") });
      }

      let loginUserRoles: string[] = []
      let userRoles: any[] = [];
      const platform = getPlatformFromRequest(req);

      if (req.user.user_role_id && platform == 1) {
        if (req.user.organization_id) {
          // Use MORole system
          const moRole = await getRolesMo([req.user.user_role_id]);

          if (moRole && moRole.length > 0) {
            loginUserRoles = [moRole[0].role_name];
            userRoles = moRole;
          }
        }
      } else {
        userRoles = await getUserAllRoles(req?.user?.id);
        loginUserRoles =
          userRoles.length > 0
            ? _.map(userRoles, (userRole: any) => userRole.role_name)
            : [];
      }

      // Platform access validation using MORole permissions
      const platformType = req.headers["platform-type"] as string;

      if (req.user.user_role_id && platform == 1) {
        // Use MORole system - check platform permissions dynamically
        if (req.user.organization_id) {
          const hasAccess = await checkPlatformAccess(
            req.user,
            req.user.organization_id,
            platformType
          );

          if (!hasAccess) {
            return res
              .status(StatusCodes.UNAUTHORIZED)
              .json({ status: false, message: res.__("FAIL_TOKEN_EXPIRED") });
          }
        }
      } else {
        const normalUser = [...NORMAL_USER];
        const adminSideUser = [...ADMIN_SIDE_USER];

        if (
          !loginUserRoles.some((item: any) => adminSideUser.includes(item)) &&
          req.headers["platform-type"] == "web"
        ) {
          return res
            .status(StatusCodes.UNAUTHORIZED)
            .json({ status: false, message: res.__("ERROR_INVALID_TOKEN") });
        }

        if (
          !loginUserRoles.some((item: any) => normalUser.includes(item)) &&
          (req.headers["platform-type"] == "ios" ||
            req.headers["platform-type"] == "android")
        ) {
          return res
            .status(StatusCodes.UNAUTHORIZED)
            .json({ status: false, message: res.__("ERROR_INVALID_TOKEN") });
        }
        if (userRoles.length > 0) {
          const roleData: any = userRoles[0];

          if (process.env.NEXT_NODE_ENV !== "staging" && roleData.role_name !== ROLE_CONSTANT.SUPER_ADMIN) {
            let deviceType = req.headers["platform-type"];
            deviceType = deviceType == "ios" ? "android" : deviceType;
            const session = await getUserSession(token, deviceType);

            if (!session) {
              return res
                .status(StatusCodes.UNAUTHORIZED)
                .json({ status: false, message: res.__("ERROR_INVALID_TOKEN") });
            }
          }
        }
      }
      req.user.roles = userRoles;
      next();
    }
  } catch (e: any) {
    console.log(e);
    if (e.message == "jwt malformed") {
      return res
        .status(StatusCodes.UNAUTHORIZED)
        .send({ status: false, message: res.__("ERR_TOKEN_NOT_FOUND") });
    } else {
      if (e.name == "TokenExpiredError") {
        return res.status(StatusCodes.UNAUTHORIZED).send({
          status: false,
          message: res.__("ERROR_INVALID_TOKEN"),
        });
      } else {
        return res.status(401).send({ status: false, message: e.message });
      }
    }
  }
};

export default userAuth;
