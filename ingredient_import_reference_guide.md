# Ingredient Import Reference Guide

## Required Fields
- **ingredient_name**: Name of the ingredient (2-100 characters)
- **cost_per_unit**: Cost per unit (positive number with up to 2 decimal places)
- **unit_of_measure**: Unit of measurement (must match exact names from list below)

## Optional Fields
- **ingredient_description**: Description (up to 1000 characters)
- **ingredient_status**: Status of ingredient (default: active)
- **waste_percentage**: Waste percentage (0-100, up to 2 decimal places)
- **categories**: Comma-separated category names
- **nutritions**: Nutrition information in format "attribute:unit:value"
- **allergens**: Comma-separated allergen names
- **dietary**: Comma-separated dietary attribute names

## Valid Values

### ingredient_status
- `active` (default)
- `inactive`

### unit_of_measure (exact names only)
- `g` - grams
- `kg` - kilograms
- `ml` - milliliters
- `liter` - liters
- `unit` - individual units
- `oz (ounce)` - ounces
- `lb (pound)` - pounds
- `tbsp (tablespoon)` - tablespoons
- `tsp (teaspoon)` - teaspoons
- `cup` - cups
- `pint` - pints
- `quart` - quarts
- `gallon` - gallons
- `each` - each
- `clove` - cloves
- `ball` - balls
- `slice` - slices
- `serving` - servings
- `leaf` - leaves
- `pinch` - pinches
- `wedge` - wedges
- `sprig` - sprigs
- `pack` - packs
- `case` - cases
- `dozen` - dozens

### categories (comma-separated, exact names only)
- `Dairy` - Dairy products
- `Meat` - Meat products
- `Poultry` - Poultry products
- `Seafood` - Seafood products
- `Vegetables` - Vegetables
- `Fruits` - Fruits
- `Grains` - Grains and cereals
- `Nuts` - Nuts and seeds
- `Herbs & Spices` - Herbs and spices
- `Oils` - Oils and fats
- `Condiments` - Condiments and sauces
- `Baking` - Baking ingredients
- `Dry Goods` - Dry goods and pulses
- `Beverages` - Beverages and drinks

### nutritions (format: attribute:unit:value, comma-separated)
Available nutrition attributes:
- `Energy` - Energy content
- `Fat` - Total fat content
- `Saturates` - Saturated fats
- `Carbohydrate` - Total carbohydrates
- `Sugars` - Sugar content
- `Fibre` - Dietary fiber
- `Protein` - Protein content
- `Salt` - Salt/sodium content

**Format Examples:**
- `Protein:g:25.5` - 25.5 grams of protein
- `Energy:kcal:150` - 150 kilocalories
- `Fat:g:10.2,Protein:g:25.5` - Multiple nutrition values

### allergens (comma-separated, exact names only)
- `Gluten` - Contains gluten
- `Crustaceans` - Contains crustaceans
- `Eggs` - Contains eggs
- `Fish` - Contains fish
- `Peanuts` - Contains peanuts
- `Soy` - Contains soy
- `Milk` - Contains milk/dairy
- `Tree Nuts` - Contains tree nuts
- `Celery` - Contains celery
- `Mustard` - Contains mustard
- `Sesame` - Contains sesame
- `Sulphites` - Contains sulphites
- `Lupin` - Contains lupin
- `Molluscs` - Contains molluscs
- `None` - No allergens

### dietary (comma-separated, exact names only)
- `Vegan` - Suitable for vegans
- `Vegetarian` - Suitable for vegetarians
- `Gluten-Free` - Gluten-free
- `Halal` - Halal certified
- `Kosher` - Kosher certified
- `Dairy-Free` - Dairy-free
- `Nut-Free` - Nut-free
- `Low-Carb` - Low carbohydrate
- `Keto` - Ketogenic diet friendly
- `Paleo` - Paleo diet friendly
- `None` - No specific dietary attributes

## Example Rows

```csv
ingredient_name,ingredient_description,ingredient_status,waste_percentage,cost_per_unit,unit_of_measure,categories,nutritions,allergens,dietary
Chicken Breast,Fresh organic chicken breast,active,5.5,12.99,g,Meat,Protein:g:25.5,None,None
Salmon Fillet,Wild-caught Atlantic salmon,active,8,18.5,g,Seafood,Protein:g:22.0,Fish,None
Whole Wheat Flour,Organic whole wheat flour,active,2,3.25,g,Grains,Protein:g:13.2,Gluten,Vegetarian
Greek Yogurt,Plain Greek yogurt,active,3,5.49,g,Dairy,Protein:g:10.0,Milk,Vegetarian
Almonds,Raw unsalted almonds,active,5,12.99,g,Nuts,Protein:g:21.2,Tree Nuts,Vegan
```

## Common Mistakes to Avoid

1. **Case Sensitivity**: All values are case-sensitive. Use exact capitalization.
2. **Unit Names**: Use exact unit names from the list (e.g., "g" not "grams")
3. **Status Values**: Use lowercase "active" or "inactive"
4. **Multiple Values**: Use commas to separate multiple categories, allergens, or dietary attributes
5. **Nutrition Format**: Always use "attribute:unit:value" format
6. **Empty Values**: Use "None" for allergens or dietary when there are none

## File Format
- Save as CSV (Comma Separated Values)
- Use UTF-8 encoding
- First row must contain headers exactly as shown
- Remove any comment lines starting with #
