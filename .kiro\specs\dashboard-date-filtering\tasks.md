# Implementation Plan

- [ ] 1. Create date validation and helper utilities


  - Create centralized date validation helper with whitelist of valid date ranges
  - Implement UTC conversion utilities for consistent timezone handling
  - Add date range sanitization and error response formatting
  - _Requirements: 2.5, 4.4, 5.2_

- [ ] 2. Create query filter builder utility
  - Implement standardized date filter SQL generation for database queries
  - Create parameter replacement helper for safe date filtering
  - Add support for different date column names and table aliases
  - _Requirements: 4.1, 4.2, 4.3_

- [ ] 3. Fix getRecipeCount method date filtering
  - Update getRecipeCount to properly apply startDate and endDate filters to created_at column
  - Ensure organization filtering works correctly with date filtering
  - Add error handling for date filter failures
  - _Requirements: 1.1, 1.2, 3.1_

- [ ] 4. Fix getTotalRecipeViews method date filtering
  - Update getTotalRecipeViews query to properly filter analytics records by date range
  - Ensure the JOIN with recipe table maintains date filtering on analytics.created_at
  - Add proper error handling for failed date-filtered queries
  - _Requirements: 1.1, 1.2, 3.4_

- [ ] 5. Fix getTopCategory method date filtering
  - Update getTopCategory query to filter recipes by the specified date range
  - Apply date filtering to recipe.updated_at or created_at column consistently
  - Ensure category calculations only include recipes from the specified period
  - _Requirements: 1.1, 1.2, 3.2_

- [ ] 6. Fix getContactSubmissions method date filtering
  - Verify getContactSubmissions properly applies date range to analytics.created_at
  - Ensure organization filtering works correctly with date filtering
  - Add validation that only contact submissions within date range are counted
  - _Requirements: 1.1, 1.2, 3.4_

- [ ] 7. Fix getTotalBookmarks method date filtering
  - Update getTotalBookmarks to filter bookmarks by creation date within specified range
  - Add date filtering to the bookmark query using appropriate date column
  - Ensure organization filtering combines properly with date filtering
  - _Requirements: 1.1, 1.2, 3.4_

- [ ] 8. Fix getRecipeViewsTrend method date filtering
  - Ensure getRecipeViewsTrend properly filters analytics records by date range
  - Verify that both recipe organization filter and analytics date filter work together
  - Test that trend data only includes views from the specified time period
  - _Requirements: 1.1, 1.2, 3.5_

- [ ] 9. Fix getCategoryPerformance method date filtering
  - Update getCategoryPerformance to filter analytics by date range in the JOIN
  - Ensure recipe filtering and analytics date filtering work together correctly
  - Verify category performance metrics only reflect the specified time period
  - _Requirements: 1.1, 1.2, 3.6_

- [ ] 10. Fix getUserEngagementHeatmap method date filtering
  - Verify getUserEngagementHeatmap properly applies date range to analytics.created_at
  - Ensure the BETWEEN clause correctly filters engagement data by time period
  - Test that heatmap data only shows activity from the specified date range
  - _Requirements: 1.1, 1.2, 3.6_

- [ ] 11. Fix getConversionFunnel method date filtering
  - Update getConversionFunnel to properly filter all UNION queries by date range
  - Ensure each funnel stage (views, clicks, contacts) respects the date filter
  - Verify conversion rate calculations only use data from the specified period
  - _Requirements: 1.1, 1.2, 3.7_

- [ ] 12. Fix getRecentActivity method date filtering
  - Update getRecentActivity to properly apply date range when startDate/endDate provided
  - Ensure recent activity only shows activities within the specified time period
  - Add proper date filtering to the analytics query with organization filter
  - _Requirements: 1.1, 1.2, 3.8_

- [ ] 13. Fix getHighestImpressionRecipe method date filtering
  - Add date range parameters to getHighestImpressionRecipe method signature
  - Update the analytics query to filter impressions by the specified date range
  - Ensure highest impression calculation only considers the filtered time period
  - _Requirements: 1.1, 1.2, 3.3_

- [ ] 14. Enhance date range validation in dashboard controller
  - Update getDashboardOverview controller to validate date_range parameter against whitelist
  - Add proper error responses for invalid date range values
  - Implement fallback to default date range for invalid inputs
  - _Requirements: 2.5, 5.2, 5.4_

- [ ] 15. Add comprehensive error handling and logging
  - Add logging for date range calculations and database query performance
  - Implement structured error responses for date filtering failures
  - Add debug logging for date filter SQL generation and parameter values
  - _Requirements: 5.1, 5.3, 5.4_

- [ ] 16. Create comprehensive test cases for date filtering
  - Write unit tests for date range calculations including edge cases
  - Create integration tests for dashboard API with various date ranges
  - Add test cases for timezone handling and UTC consistency
  - Test financial year calculations with different organization settings
  - _Requirements: 4.3, 4.4, 4.5_