# CTA Analytics Comprehensive Test Plan

## ✅ FIXED ISSUES SUMMARY

### 1. **Sorting Fixed**
- ✅ **"Last Clicked At" sorting** - Now uses `sort_by=created_at` to sort by `last_clicked_at`
- ✅ **Post-aggregation sorting** - <PERSON><PERSON> clicks and created_at after data aggregation
- ✅ **Database query optimization** - Skips ORDER BY for aggregated fields

### 2. **Export Data Consistency Fixed**
- ✅ **Query alignment** - Export query now matches main query exactly
- ✅ **INNER JOIN with status filter** - Only non-deleted recipes: `r.recipe_status != 'deleted'`
- ✅ **Same filters applied** - Organization, CTA type, and search filters consistent

### 3. **Filter Consistency Fixed**
- ✅ **Organization filter** - Consistent across main and export queries
- ✅ **CTA type filter** - Proper mapping and JSON pattern matching
- ✅ **Search filter** - Enhanced to search recipe names, CTA types, and display names

### 4. **Time Format Fixed**
- ✅ **12-hour format with AM/PM** - `TimezoneHelper.formatForCSV()` and `formatForExcel()`
- ✅ **IST timezone** - Consistent UTC+5:30 conversion

## 🧪 TEST SCENARIOS

### **Test 1: Basic Data Display**
```
GET /api/v1/private/analytics/cta-clicks
Expected: Shows all 4 CTA entries with proper recipe names, CTA types, clicks, and timestamps
```

### **Test 2: Sorting by Last Clicked At**
```
GET /api/v1/private/analytics/cta-clicks?sort_by=created_at&sort_order=desc
Expected: Most recent clicks first (17-07-2025 entries before 16-07-2025)

GET /api/v1/private/analytics/cta-clicks?sort_by=created_at&sort_order=asc  
Expected: Oldest clicks first (16-07-2025 entries before 17-07-2025)
```

### **Test 3: Sorting by Clicks**
```
GET /api/v1/private/analytics/cta-clicks?sort_by=clicks&sort_order=desc
Expected: Highest clicks first (39 > 7 > 2 > 1)

GET /api/v1/private/analytics/cta-clicks?sort_by=clicks&sort_order=asc
Expected: Lowest clicks first (1 > 2 > 7 > 39)
```

### **Test 4: CTA Type Filter**
```
GET /api/v1/private/analytics/cta-clicks?cta_type=contact_form
Expected: Only "Contact Form" entries (universal sweets, Masala Tea)

GET /api/v1/private/analytics/cta-clicks?cta_type=custom_cta
Expected: Only "Custom CTA" entries (Palak Paneer recipe, Masala Tea)
```

### **Test 5: Search Filter**
```
GET /api/v1/private/analytics/cta-clicks?search=Masala
Expected: Only "Masala Tea" entries

GET /api/v1/private/analytics/cta-clicks?search=Contact Form
Expected: All "Contact Form" entries
```

### **Test 6: Export Functionality**
```
GET /api/v1/private/analytics/cta-analytics/export?format=csv
Expected: CSV with all 4 rows, proper timestamps with AM/PM

GET /api/v1/private/analytics/cta-analytics/export?format=excel
Expected: Excel with all 4 rows, proper timestamps with AM/PM
```

### **Test 7: Combined Filters**
```
GET /api/v1/private/analytics/cta-clicks?cta_type=contact_form&sort_by=clicks&sort_order=desc
Expected: Contact Form entries sorted by clicks (39 > 7)
```

## 🎯 EXPECTED RESULTS

### **Data Structure:**
```json
{
  "status": true,
  "data": [
    {
      "recipe_name": "Masala Tea",
      "recipe_id": 123,
      "cta_type": "Contact Form",
      "clicks": 39,
      "last_clicked_at": "2025-07-16T..."
    }
  ]
}
```

### **Export CSV Format:**
```
ID,Recipe Name,CTA Type,Clicks,Last Clicked At
1,"Masala Tea","Contact Form",39,"16-07-2025 05:30:00 PM"
2,"universal sweets","Contact Form",7,"17-07-2025 05:30:00 PM"
```

### **Time Format Examples:**
- ✅ `16-07-2025 05:30:00 PM` (instead of `16-07-2025 12:00 AM`)
- ✅ `17-07-2025 09:15:30 AM` (instead of `17-07-2025 12:00 AM`)

## 🔧 KEY IMPROVEMENTS MADE

1. **Unified Sort Logic** - `created_at` sorts by `last_clicked_at` for aggregated data
2. **Perfect Query Alignment** - Export and main queries are identical
3. **Enhanced Search** - Searches both stored values and display names
4. **Consistent Filters** - All filters work the same in main and export
5. **Proper Time Format** - 12-hour format with AM/PM in exports
6. **Accurate Aggregation** - Correct click counts and latest timestamps

## ✅ VERIFICATION CHECKLIST

- [ ] Sorting by "Last Clicked At" works (ASC/DESC)
- [ ] Sorting by "Clicks" works (ASC/DESC)  
- [ ] CTA Type filter shows correct entries
- [ ] Search filter finds recipes and CTA types
- [ ] Export shows all 4 rows (not just 1)
- [ ] Export timestamps show proper AM/PM format
- [ ] Combined filters work together
- [ ] Pagination works correctly
- [ ] Data consistency between table and export
