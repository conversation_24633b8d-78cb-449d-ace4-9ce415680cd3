# 🎨 Default Icons Seeder Guide

This guide explains how to use the production-ready icon seeder that follows TTH-backend-server patterns and integrates seamlessly with the existing `iconSeeder.service.ts`.

## 📋 Overview

The **Default Icons Seeder** (`20250117120000-mo_default_icons.js`) is a Sequelize-based seeder that uploads default icons for:

- **Recipe Categories** (Main Course, Appetizer, <PERSON>sert, etc.)
- **Ingredient Categories** (Dairy, Meat, Vegetables, etc.)  
- **Food Attributes** (Allergens like Gluten, Nuts, etc.)

## 🚀 Quick Start

### Basic Usage

```bash
# Run the icon seeder (default environment)
npm run icons:seed

# Run for specific environment
npm run icons:seed:dev
npm run icons:seed:staging
npm run icons:seed:prod
```

### Force Re-upload

```bash
# Force re-upload even if icons already exist
npm run icons:seed:force

# Force re-upload for specific environment
npm run icons:seed:force:dev
npm run icons:seed:force:staging
npm run icons:seed:force:prod
```

### Complete Seeding

```bash
# Run all data seeders + icons
npm run seed:complete
```

## 🔧 Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `FORCE_ICON_UPLOAD` | Force re-upload icons even if they exist | `false` |
| `NODE_ENV` | Environment (affects S3 bucket) | `staging` |

## 📊 What the Seeder Does

### ✅ Smart Existence Check
- Counts existing system icons in database
- Skips upload if 20+ icons already exist (unless forced)
- Shows current status before proceeding

### 📤 Icon Upload Process
- Uses existing `iconSeeder.service.ts` for actual uploads
- Uploads to configured S3 bucket
- Creates entries in `nv_items` table
- Links icons to categories/attributes

### 📈 Comprehensive Reporting
- Real-time progress updates
- Detailed success/failure breakdown
- Before/after statistics
- Clear error messages with troubleshooting tips

### 🔄 Rollback Support
- Proper `down()` method for rollbacks
- Unlinks icons from categories/attributes
- Removes icon database records
- Preserves S3 files for data integrity

## 🎯 Icon Categories

### Recipe Categories (11 icons)
```
Main Course → Main Courses Stroke Black.png
Appetizer → Appetizers & Starters Stroke Black.png
Dessert → Dessert cake Stroke Black.png
Beverage → Beverages & Drinks Stroke Black.png
Soup → Soups Stroke Black.png
Salad → salad Stroke Black.png
Snack → snacks Stroke Black.png
Breakfast → breakfast Stroke Black.png
Baked Goods → Baked Goods Stroke Black.png
Sauces & Dressings → sauce& Dressings Stroke Black.png
Side Dishes → Side Dishes Stroke Black.png
```

### Ingredient Categories (14 icons)
```
Dairy → milk Stroke Black.png
Meat → meat Stroke Black.png
Poultry → Poultry Stroke Black.png
Seafood → Sea Food Stroke Black.png
Vegetables → vegetables Stroke Black.png
Fruits → fruits Stroke Black.png
Grains → Grains Stroke Black.png
Nuts → Nuts & Seeds Stroke Black.png
Herbs & Spices → Herbs & Spices Stroke Black.png
Oils → oil Stroke Black.png
Condiments → sauce Stroke Black.png
Baking → baking & sweeteners Stroke Black.png
Dry Goods → Dry Goods & Pulses Stroke Black.png
Beverages → Beverages & Drinks Stroke Black.png
```

### Allergens (14 icons)
```
Gluten → gluten Stroke.png
Crustaceans → Crustaceans Stroke.png
Eggs → Egg Stroke.png
Fish → Fish Stroke.png
Milk → Milk Stroke Blue.png
Molluscs → Molluscs Stroke.png
Peanuts → Peanuts Stroke.png
Tree Nuts → Tree Nut Stroke.png
Soy → soybean Stroke.png
Sesame → Sesame Stroke.png
Celery → celery Stroke.png
Mustard → Mustard Stroke.png
Sulphites → Sulphite Stroke.png
Lupin → lupin Stroke.png
```

## 📂 File Structure

```
backend-recipes-ms/
├── src/
│   ├── icons/                          # Icon source files
│   │   ├── Allergens/                  # Allergen icons (.png)
│   │   ├── recipe_category/            # Recipe category icons (.png)
│   │   └── ingredient_category/        # Ingredient category icons (.png)
│   ├── services/
│   │   └── iconSeeder.service.ts       # Icon upload service
│   └── seeders/
│       └── 20250117120000-mo_default_icons.js  # Icon seeder
└── package.json                        # Updated with new commands
```

## 🛠️ Technical Details

### Database Integration
- **Items Table**: `nv_items` - Stores icon metadata
- **Categories**: `mo_category.category_icon` - Links to item ID  
- **Attributes**: `mo_food_attributes.attribute_icon` - Links to item ID

### S3 Storage
- **Bucket**: Uses `NODE_ENV` as bucket name
- **Path Pattern**: `icons/{category}/{sanitized-name}-icon.png`
- **Deduplication**: Hash-based duplicate detection

### Error Handling
- Comprehensive try-catch blocks
- Detailed error messages
- Troubleshooting guidance
- Graceful failure handling

## 🔍 Troubleshooting

### Common Issues

#### 1. AWS S3 Configuration
```bash
# Ensure AWS credentials are configured
aws configure list

# Check S3 bucket access
aws s3 ls s3://your-bucket-name/
```

#### 2. Icon Files Missing
```bash
# Verify icon files exist
ls -la src/icons/Allergens/
ls -la src/icons/recipe_category/
ls -la src/icons/ingredient_category/
```

#### 3. Database Connection
```bash
# Test database connection
npm run db:migrate:dev
```

#### 4. Service Dependencies
```bash
# Verify iconSeeder service exists
ls -la src/services/iconSeeder.service.ts
```

### Error Messages

| Error | Cause | Solution |
|-------|-------|----------|
| `IconSeeder service not found` | Missing service file | Check `iconSeeder.service.ts` exists |
| `Icon file not found` | Missing icon files | Verify files in `src/icons/` directories |
| `Upload failed` | S3 configuration | Check AWS credentials and bucket access |
| `All icon uploads failed` | Multiple issues | Check AWS config, file paths, database |

## 🔄 Rollback

### Undo Icon Seeding

```bash
# Rollback using Sequelize
npx sequelize-cli db:seed:undo --seed 20250117120000-mo_default_icons.js

# Or manually for specific environment
npx sequelize-cli db:seed:undo --seed 20250117120000-mo_default_icons.js --env production
```

### What Rollback Does
1. ✅ Unlinks icons from categories (`category_icon = NULL`)
2. ✅ Unlinks icons from food attributes (`attribute_icon = NULL`)  
3. ✅ Removes icon records from `nv_items` table
4. ⚠️ Preserves S3 files (for data integrity)

## 🚦 Production Deployment

### Pre-deployment Checklist
- [ ] AWS S3 credentials configured
- [ ] All icon files present in `src/icons/`
- [ ] Database migrations completed
- [ ] Test in staging environment first

### Deployment Commands
```bash
# 1. Run migrations first
npm run db:migrate:prod

# 2. Run data seeders
npm run db:seed:prod

# 3. Upload icons
npm run icons:seed:prod

# Or run all together
NODE_ENV=production npm run seed:complete
```

## 📈 Performance & Monitoring

### Expected Performance
- **Upload Time**: ~30-60 seconds for all icons
- **S3 Operations**: ~39 files uploaded
- **Database Operations**: ~39 item records created

### Monitoring
```sql
-- Check icon counts
SELECT 
  item_category,
  COUNT(*) as count
FROM nv_items 
WHERE item_organization_id IS NULL 
  AND item_category IN ('category_icon', 'attribute_icon')
  AND item_status = 'active'
GROUP BY item_category;

-- Check linked categories
SELECT COUNT(*) as categories_with_icons
FROM mo_category 
WHERE organization_id IS NULL 
  AND is_system_category = true 
  AND category_icon IS NOT NULL;

-- Check linked attributes  
SELECT COUNT(*) as attributes_with_icons
FROM mo_food_attributes 
WHERE organization_id IS NULL 
  AND is_system_attribute = true 
  AND attribute_icon IS NOT NULL;
```

## 💡 Best Practices

### 1. Environment Management
- Always test in development/staging first
- Use environment-specific commands
- Verify AWS configuration per environment

### 2. Icon Management
- Keep icon files in version control
- Use consistent naming conventions
- Optimize file sizes for web delivery

### 3. Database Management
- Run migrations before seeders
- Monitor database performance during seeding
- Use rollback capability when needed

### 4. Error Handling
- Check logs for detailed error information
- Use force flag judiciously
- Verify S3 and database states after failures

## 🔗 Integration

### With Existing Services
- ✅ Uses existing `iconSeeder.service.ts`
- ✅ Follows existing upload patterns
- ✅ Integrates with current database schema
- ✅ Compatible with existing category/attribute seeders

### API Integration
Once icons are seeded, they're automatically available through:
- Category management APIs
- Attribute management APIs  
- Recipe/ingredient creation workflows
- Admin panel interfaces

---

## 📞 Support

For issues or questions:
1. Check the troubleshooting section above
2. Verify all prerequisites are met
3. Check the detailed console logs
4. Review the iconSeeder.service.ts logs

The seeder provides comprehensive logging and error messages to help diagnose and resolve issues quickly. 