import { sequelize } from "../models";
import { Recipe, RecipeStatus } from "../models/Recipe";
import { checkAndUpdateRecipeCosts } from "../helper/cost-freshness.helper";
import { Op } from "sequelize";

interface RecipeCostFreshnessStats {
  totalRecipesChecked: number;
  recipesWithOutdatedCosts: number;
  recipesUpdated: number;
  recipesUpdatesFailed: number;
  organizationsProcessed: string[];
  errors: string[];
  executionTime: number;
}

/**
 * Cron job to automatically update recipe ingredient costs based on freshness status
 * Only updates recipes where is_cost_manual = false and ingredients are outdated
 */
export const recipeCostFreshnessCronJob = async (): Promise<RecipeCostFreshnessStats> => {
  const startTime = Date.now();
  const stats: RecipeCostFreshnessStats = {
    totalRecipesChecked: 0,
    recipesWithOutdatedCosts: 0,
    recipesUpdated: 0,
    recipesUpdatesFailed: 0,
    organizationsProcessed: [],
    errors: [],
    executionTime: 0
  };

  try {
    console.log('Starting recipe cost freshness cron job...');

    // Get all active organizations with recipes that have automatic cost calculation enabled
    const organizations = await getActiveOrganizationsWithAutoCalculationRecipes();
    stats.organizationsProcessed = organizations;

    console.log(`Processing ${organizations.length} organizations for recipe cost freshness updates`);

    // Process each organization separately
    for (const organizationId of organizations) {
      try {
        const orgStats = await processOrganizationRecipeCostFreshness(organizationId);

        stats.totalRecipesChecked += orgStats.totalRecipesChecked;
        stats.recipesWithOutdatedCosts += orgStats.recipesWithOutdatedCosts;
        stats.recipesUpdated += orgStats.recipesUpdated;
        stats.recipesUpdatesFailed += orgStats.recipesUpdatesFailed;
        stats.errors.push(...orgStats.errors);

        console.log(`Organization ${organizationId}: ${orgStats.recipesWithOutdatedCosts} recipes with outdated costs, ${orgStats.recipesUpdated} recipes updated`);

      } catch (error: any) {
        const errorMsg = `Failed to process recipe cost freshness for organization ${organizationId}: ${error.message}`;
        stats.errors.push(errorMsg);
        console.error(errorMsg);
      }
    }

    stats.executionTime = Date.now() - startTime;

    // Log summary
    console.log('Recipe cost freshness cron job completed:');
    console.log(`- Total recipes checked: ${stats.totalRecipesChecked}`);
    console.log(`- Recipes with outdated costs: ${stats.recipesWithOutdatedCosts}`);
    console.log(`- Recipes updated: ${stats.recipesUpdated}`);
    console.log(`- Recipe updates failed: ${stats.recipesUpdatesFailed}`);
    console.log(`- Organizations processed: ${stats.organizationsProcessed.length}`);
    console.log(`- Execution time: ${stats.executionTime}ms`);

    if (stats.errors.length > 0) {
      console.log(`- Errors encountered: ${stats.errors.length}`);
      stats.errors.forEach(error => console.error(`  - ${error}`));
    }

    return stats;

  } catch (error: any) {
    stats.executionTime = Date.now() - startTime;
    stats.errors.push(`Recipe cost freshness cron job failed: ${error.message}`);
    console.error('Recipe cost freshness cron job failed:', error);
    throw error;
  }
};

/**
 * Get all active organizations that have recipes with automatic cost calculation enabled
 */
const getActiveOrganizationsWithAutoCalculationRecipes = async (): Promise<string[]> => {
  try {
    const query = `
      SELECT DISTINCT r.organization_id
      FROM mo_recipe r
      WHERE r.recipe_status = '${RecipeStatus.publish}'
        AND r.organization_id IS NOT NULL
        AND r.organization_id != ''
        AND (r.is_cost_manual = false OR r.is_cost_manual IS NULL)
      ORDER BY r.organization_id
    `;

    const results = await sequelize.query(query, {
      type: sequelize.QueryTypes.SELECT
    }) as Array<{ organization_id: string }>;

    return results.map(row => row.organization_id);

  } catch (error) {
    console.error('Error getting organizations with auto-calculation recipes:', error);
    throw error;
  }
};

/**
 * Process recipe cost freshness updates for a specific organization
 */
const processOrganizationRecipeCostFreshness = async (organizationId: string): Promise<{
  totalRecipesChecked: number;
  recipesWithOutdatedCosts: number;
  recipesUpdated: number;
  recipesUpdatesFailed: number;
  errors: string[];
}> => {
  const orgStats = {
    totalRecipesChecked: 0,
    recipesWithOutdatedCosts: 0,
    recipesUpdated: 0,
    recipesUpdatesFailed: 0,
    errors: [] as string[]
  };

  try {
    // Get all active recipes with automatic cost calculation enabled
    const recipes = await Recipe.findAll({
      where: {
        organization_id: organizationId,
        recipe_status: RecipeStatus.publish,
        [Op.or]: [
          { is_cost_manual: false },
          { is_cost_manual: null }
        ]
      },
      attributes: ['id', 'recipe_title', 'is_cost_manual', 'ingredient_costs_updated_at'],
      order: [['id', 'ASC']]
    });

    orgStats.totalRecipesChecked = recipes.length;

    if (recipes.length === 0) {
      return orgStats;
    }

    console.log(`Checking ${recipes.length} recipes with auto-calculation enabled for organization ${organizationId}`);

    // Process each recipe to check freshness and update if needed
    for (const recipe of recipes) {
      try {
        console.log(`Checking recipe ${recipe.id} (${recipe.recipe_title}) for cost freshness`);

        // Use the cost-freshness helper to check and update recipe costs
        const updateResult = await checkAndUpdateRecipeCosts(
          recipe.id,
          organizationId,
          {
            forceUpdate: false, // Only update if actually outdated
            autoUpdate: true,   // Enable auto-update
            transaction: undefined
          }
        );

        if (updateResult.wasOutdated) {
          orgStats.recipesWithOutdatedCosts++;

          if (updateResult.wasUpdated) {
            orgStats.recipesUpdated++;
            console.log(`Recipe ${recipe.id} costs updated successfully. New total cost: ${updateResult.totalCost}`);
          } else {
            orgStats.recipesUpdatesFailed++;
            const errorMsg = `Recipe ${recipe.id} had outdated costs but update failed`;
            orgStats.errors.push(errorMsg);
            console.error(errorMsg);
          }
        }

      } catch (error: any) {
        orgStats.recipesUpdatesFailed++;
        const errorMsg = `Error processing recipe ${recipe.id}: ${error.message}`;
        orgStats.errors.push(errorMsg);
        console.error(errorMsg);
      }
    }

    return orgStats;

  } catch (error: any) {
    orgStats.errors.push(`Organization recipe processing failed: ${error.message}`);
    throw error;
  }
};

export default {
  recipeCostFreshnessCronJob
};
