import { Request, Response } from "express";
import { StatusCodes } from "http-status-codes";
import ContactUs from "../models/ContactUs";
import { Op } from "sequelize";
import { Transaction<PERSON>anager, <PERSON>rror<PERSON><PERSON><PERSON> } from "../helper/transaction.helper";
import { Valida<PERSON><PERSON>elper } from "../helper/validation.helper";
import TimezoneHelper from "../helper/timezone.helper";
import * as XLSX from "xlsx";
import * as csv from "csv-writer";

/**
 * Create a new contact us submission
 * @route POST /api/v1/public/contact-us
 */
const createContactUs = async (req: Request, res: Response): Promise<any> => {
  const transactionManager = new TransactionManager();

  try {
    // Input validation and sanitization
    const sanitizedBody = ValidationHelper.sanitizeInput(req.body);
    const { name, email, mobile, message, recipe_id } = sanitizedBody;

    // Validate required fields
    const errors: string[] = [];

    const nameError = ValidationHelper.validateRequiredString(
      name,
      "name",
      2,
      100
    );
    if (nameError) errors.push(nameError);

    const emailError = ValidationHelper.validateEmail(email, "email");
    if (emailError) errors.push(emailError);

    const messageError = ValidationHelper.validateRequiredString(
      message,
      "message",
      10,
      1000
    );
    if (messageError) errors.push(messageError);

    if (mobile) {
      const mobileError = ValidationHelper.validateOptionalString(
        mobile,
        "mobile",
        20
      );
      if (mobileError) errors.push(mobileError);
    }

    if (recipe_id) {
      const recipeIdError = ValidationHelper.validatePositiveInteger(
        recipe_id,
        "recipe_id"
      );
      if (recipeIdError) errors.push(recipeIdError);
    }

    if (errors.length > 0) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: "Validation failed",
        errors,
      });
    }

    // Start transaction
    const transaction = await transactionManager.start();

    const contact = await ContactUs.create(
      {
        name: name.trim(),
        email: email.trim().toLowerCase(),
        mobile: mobile?.trim() || null,
        message: message.trim(),
        recipe_id: recipe_id || null,
      },
      { transaction }
    );

    await transactionManager.commit();

    return res.status(StatusCodes.CREATED).json({
      status: true,
      message: res.__("CONTACT_US_CREATED_SUCCESSFULLY"),
      data: { id: contact.id },
    });
  } catch (error) {
    await transactionManager.rollback();
    return ErrorHandler.createErrorResponse(
      error,
      res,
      "Error creating contact submission"
    );
  }
};

/**
 * Get all contact us submissions
 * @route GET /api/v1/public/contact-us/list
 */
const getAllContactUs = async (req: Request, res: Response): Promise<any> => {
  try {
    // Input validation and sanitization
    const sanitizedQuery = ValidationHelper.sanitizeInput(req.query);
    const { page, size, search = "", recipe_id } = sanitizedQuery;

    // Validate pagination parameters
    const pageNumber = Math.max(1, parseInt(page as string, 10) || 1);
    const pageSize = Math.min(
      100,
      Math.max(1, parseInt(size as string, 10) || 10)
    );
    const offset = (pageNumber - 1) * pageSize;

    const whereClause: any = {};

    // Validate and apply search filter
    if (search && typeof search === "string" && search.trim()) {
      const searchTerm = search.trim();
      if (searchTerm.length >= 2) {
        // Minimum search length
        whereClause[Op.or] = [
          { name: { [Op.like]: `%${searchTerm}%` } },
          { email: { [Op.like]: `%${searchTerm}%` } },
          { message: { [Op.like]: `%${searchTerm}%` } },
        ];
      }
    }

    // Validate and apply recipe filter
    if (recipe_id) {
      const recipeIdNum = parseInt(recipe_id as string, 10);
      if (!isNaN(recipeIdNum) && recipeIdNum > 0) {
        whereClause.recipe_id = recipeIdNum;
      }
    }

    const { rows: contacts, count } = await ContactUs.findAndCountAll({
      where: whereClause,
      limit: pageSize,
      offset: offset,
      order: [["created_at", "DESC"]],
    });

    const totalPages = Math.ceil(count / pageSize);

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("SUCCESS_DATA_FETCHED"),
      data: contacts,
      pagination: {
        current_page: pageNumber,
        page_size: pageSize,
        total_records: count,
        total_pages: totalPages,
        has_next: pageNumber < totalPages,
        has_prev: pageNumber > 1,
      },
    });
  } catch (error) {
    return ErrorHandler.createErrorResponse(
      error,
      res,
      "Error fetching contact submissions"
    );
  }
};

/**
 * Get single contact us by ID
 * @route GET /api/v1/public/contact-us/get/:id
 */
const getContactUsById = async (req: Request, res: Response): Promise<any> => {
  try {
    const { id } = req.params;

    // Validate ID parameter
    const idError = ValidationHelper.validatePositiveInteger(id, "id");
    if (idError) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: idError,
      });
    }

    const contact = await ContactUs.findByPk(id);

    if (!contact) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("CONTACT_US_NOT_FOUND"),
      });
    }

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("SUCCESS_DATA_FETCHED"),
      data: contact,
    });
  } catch (error) {
    return ErrorHandler.createErrorResponse(
      error,
      res,
      "Error fetching contact submission"
    );
  }
};

/**
 * Update contact us by ID
 * @route PUT /api/v1/public/contact-us/update/:id
 */
const updateContactUs = async (req: Request, res: Response): Promise<any> => {
  const transactionManager = new TransactionManager();

  try {
    const { id } = req.params;

    // Validate ID parameter
    const idError = ValidationHelper.validatePositiveInteger(id, "id");
    if (idError) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: idError,
      });
    }

    // Input validation and sanitization
    const sanitizedBody = ValidationHelper.sanitizeInput(req.body);
    const { name, email, mobile, message, recipe_id } = sanitizedBody;

    // Start transaction
    const transaction = await transactionManager.start();

    const contact = await ContactUs.findByPk(id, { transaction });

    if (!contact) {
      await transactionManager.rollback();
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("CONTACT_US_NOT_FOUND"),
      });
    }

    // Validate updated fields if provided
    const errors: string[] = [];

    if (name !== undefined) {
      const nameError = ValidationHelper.validateRequiredString(
        name,
        "name",
        2,
        100
      );
      if (nameError) errors.push(nameError);
    }

    if (email !== undefined) {
      const emailError = ValidationHelper.validateEmail(email, "email");
      if (emailError) errors.push(emailError);
    }

    if (message !== undefined) {
      const messageError = ValidationHelper.validateRequiredString(
        message,
        "message",
        10,
        1000
      );
      if (messageError) errors.push(messageError);
    }

    if (mobile !== undefined && mobile !== null) {
      const mobileError = ValidationHelper.validateOptionalString(
        mobile,
        "mobile",
        20
      );
      if (mobileError) errors.push(mobileError);
    }

    if (recipe_id !== undefined && recipe_id !== null) {
      const recipeIdError = ValidationHelper.validatePositiveInteger(
        recipe_id,
        "recipe_id"
      );
      if (recipeIdError) errors.push(recipeIdError);
    }

    if (errors.length > 0) {
      await transactionManager.rollback();
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: "Validation failed",
        errors,
      });
    }

    await contact.update(
      {
        name: name?.trim() || contact.name,
        email: email?.trim().toLowerCase() || contact.email,
        mobile: mobile?.trim() || contact.mobile,
        message: message?.trim() || contact.message,
        recipe_id: recipe_id !== undefined ? recipe_id : contact.recipe_id,
      },
      { transaction }
    );

    await transactionManager.commit();

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("CONTACT_US_UPDATED_SUCCESSFULLY"),
      data: { id: contact.id },
    });
  } catch (error) {
    await transactionManager.rollback();
    return ErrorHandler.createErrorResponse(
      error,
      res,
      "Error updating contact submission"
    );
  }
};

/**
 * Delete contact us by ID
 * @route DELETE /api/v1/public/contact-us/delete/:id
 */
const deleteContactUs = async (req: Request, res: Response): Promise<any> => {
  const transactionManager = new TransactionManager();

  try {
    const { id } = req.params;

    // Validate ID parameter
    const idError = ValidationHelper.validatePositiveInteger(id, "id");
    if (idError) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: idError,
      });
    }

    // Start transaction
    const transaction = await transactionManager.start();

    const contact = await ContactUs.findByPk(id, { transaction });

    if (!contact) {
      await transactionManager.rollback();
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("CONTACT_US_NOT_FOUND"),
      });
    }

    await contact.destroy({ transaction });

    await transactionManager.commit();

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("CONTACT_US_DELETED_SUCCESSFULLY"),
    });
  } catch (error) {
    await transactionManager.rollback();
    return ErrorHandler.createErrorResponse(
      error,
      res,
      "Error deleting contact submission"
    );
  }
};

/**
 * Export contact us submissions to Excel or CSV
 * @route GET /api/v1/public/contact-us/export
 */
const exportContactUs = async (req: Request, res: Response): Promise<any> => {
  try {
    // Input validation and sanitization
    const sanitizedQuery = ValidationHelper.sanitizeInput(req.query);
    const {
      format = "excel",
      search = "",
      recipe_id,
      start_date,
      end_date,
      status = "all",
    } = sanitizedQuery;

    // Validate format
    if (!["excel", "csv"].includes(format as string)) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: "Invalid export format. Use 'excel' or 'csv'.",
      });
    }

    const whereClause: any = {};

    // Apply search filter
    if (search && typeof search === "string" && search.trim()) {
      const searchTerm = search.trim();
      if (searchTerm.length >= 2) {
        whereClause[Op.or] = [
          { name: { [Op.like]: `%${searchTerm}%` } },
          { email: { [Op.like]: `%${searchTerm}%` } },
          { message: { [Op.like]: `%${searchTerm}%` } },
        ];
      }
    }

    // Apply recipe filter
    if (recipe_id) {
      const recipeIdNum = parseInt(recipe_id as string, 10);
      if (!isNaN(recipeIdNum) && recipeIdNum > 0) {
        whereClause.recipe_id = recipeIdNum;
      }
    }

    // Apply date range filter
    if (start_date || end_date) {
      const dateFilter: any = {};
      if (start_date) {
        dateFilter[Op.gte] = new Date(start_date as string);
      }
      if (end_date) {
        const endDateTime = new Date(end_date as string);
        endDateTime.setHours(23, 59, 59, 999); // End of day
        dateFilter[Op.lte] = endDateTime;
      }
      whereClause.created_at = dateFilter;
    }

    // Get all matching contacts
    const contacts = await ContactUs.findAll({
      where: whereClause,
      order: [["created_at", "DESC"]],
      raw: true,
    });

    if (contacts.length === 0) {
      return res.status(StatusCodes.OK).json({
        status: true,
        message: "No contact submissions found for export.",
        data: { count: 0 },
      });
    }

    // Prepare data for export
    const exportData = contacts.map((contact: any, index: number) => ({
      "S.No": index + 1,
      Name: contact.name || "",
      Email: contact.email || "",
      Mobile: contact.mobile || "",
      Subject: contact.subject || "",
      Message: contact.message || "",
      "Recipe ID": contact.recipe_id || "",
      "Submitted On": contact.created_at
        ? TimezoneHelper.formatForExcel(contact.created_at)
        : "",
      "Updated On": contact.updated_at
        ? TimezoneHelper.formatForExcel(contact.updated_at)
        : "",
    }));

    const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, "-");

    if (format === "excel") {
      // Create Excel workbook
      const workbook = XLSX.utils.book_new();
      const worksheet = XLSX.utils.json_to_sheet(exportData);

      // Set column widths
      const columnWidths = [
        { wch: 8 }, // S.No
        { wch: 20 }, // Name
        { wch: 30 }, // Email
        { wch: 15 }, // Mobile
        { wch: 25 }, // Subject
        { wch: 50 }, // Message
        { wch: 12 }, // Recipe ID
        { wch: 20 }, // Submitted On
        { wch: 20 }, // Updated On
      ];
      worksheet["!cols"] = columnWidths;

      XLSX.utils.book_append_sheet(workbook, worksheet, "Contact Submissions");

      // Generate buffer
      const buffer = XLSX.write(workbook, { type: "buffer", bookType: "xlsx" });

      // Set response headers
      res.setHeader(
        "Content-Type",
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
      );
      res.setHeader(
        "Content-Disposition",
        `attachment; filename="contact-submissions-${timestamp}.xlsx"`
      );

      return res.send(buffer);
    } else {
      // Create CSV
      const csvWriter = csv.createObjectCsvStringifier({
        header: [
          { id: "S.No", title: "S.No" },
          { id: "Name", title: "Name" },
          { id: "Email", title: "Email" },
          { id: "Mobile", title: "Mobile" },
          { id: "Subject", title: "Subject" },
          { id: "Message", title: "Message" },
          { id: "Recipe ID", title: "Recipe ID" },
          { id: "Submitted On", title: "Submitted On" },
          { id: "Updated On", title: "Updated On" },
        ],
      });

      const csvString =
        csvWriter.getHeaderString() + csvWriter.stringifyRecords(exportData);

      // Set response headers
      res.setHeader("Content-Type", "text/csv");
      res.setHeader(
        "Content-Disposition",
        `attachment; filename="contact-submissions-${timestamp}.csv"`
      );

      return res.send(csvString);
    }
  } catch (error) {
    return ErrorHandler.createErrorResponse(
      error,
      res,
      "Error exporting contact submissions"
    );
  }
};

/**
 * Bulk delete contact us submissions
 * @route DELETE /api/v1/public/contact-us/bulk-delete
 */
const bulkDeleteContactUs = async (
  req: Request,
  res: Response
): Promise<any> => {
  const transactionManager = new TransactionManager();

  try {
    // Input validation and sanitization
    const sanitizedBody = ValidationHelper.sanitizeInput(req.body);
    const { contact_ids, delete_all = false, filters = {} } = sanitizedBody;

    // Validate input
    if (
      !delete_all &&
      (!contact_ids || !Array.isArray(contact_ids) || contact_ids.length === 0)
    ) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message:
          "Please provide contact_ids array or set delete_all to true with filters.",
      });
    }

    // Start transaction
    const transaction = await transactionManager.start();

    let deletedCount = 0;
    const whereClause: any = {};

    if (delete_all) {
      // Build where clause from filters
      const { search, recipe_id, start_date, end_date } = filters;

      // Apply search filter
      if (search && typeof search === "string" && search.trim()) {
        const searchTerm = search.trim();
        if (searchTerm.length >= 2) {
          whereClause[Op.or] = [
            { name: { [Op.like]: `%${searchTerm}%` } },
            { email: { [Op.like]: `%${searchTerm}%` } },
            { message: { [Op.like]: `%${searchTerm}%` } },
          ];
        }
      }

      // Apply recipe filter
      if (recipe_id) {
        const recipeIdNum = parseInt(recipe_id as string, 10);
        if (!isNaN(recipeIdNum) && recipeIdNum > 0) {
          whereClause.recipe_id = recipeIdNum;
        }
      }

      // Apply date range filter
      if (start_date || end_date) {
        const dateFilter: any = {};
        if (start_date) {
          dateFilter[Op.gte] = new Date(start_date as string);
        }
        if (end_date) {
          const endDateTime = new Date(end_date as string);
          endDateTime.setHours(23, 59, 59, 999); // End of day
          dateFilter[Op.lte] = endDateTime;
        }
        whereClause.created_at = dateFilter;
      }

      // Delete all matching records
      deletedCount = await ContactUs.destroy({
        where: whereClause,
        transaction,
      });
    } else {
      // Validate contact IDs
      const validIds = contact_ids.filter((id: any) => {
        const numId = parseInt(id, 10);
        return !isNaN(numId) && numId > 0;
      });

      if (validIds.length === 0) {
        await transactionManager.rollback();
        return res.status(StatusCodes.BAD_REQUEST).json({
          status: false,
          message: "No valid contact IDs provided.",
        });
      }

      // Delete specific contacts
      deletedCount = await ContactUs.destroy({
        where: {
          id: {
            [Op.in]: validIds,
          },
        },
        transaction,
      });
    }

    await transactionManager.commit();

    return res.status(StatusCodes.OK).json({
      status: true,
      message: `Successfully deleted ${deletedCount} contact submission(s).`,
      data: {
        deleted_count: deletedCount,
        operation: delete_all ? "bulk_delete_filtered" : "bulk_delete_by_ids",
      },
    });
  } catch (error) {
    await transactionManager.rollback();
    return ErrorHandler.createErrorResponse(
      error,
      res,
      "Error bulk deleting contact submissions"
    );
  }
};

export default {
  createContactUs,
  getAllContactUs,
  getContactUsById,
  updateContactUs,
  deleteContactUs,
  exportContactUs,
  bulkDeleteContactUs,
};
